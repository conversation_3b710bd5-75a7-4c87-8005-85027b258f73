"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-sliding-side-panel@2.0.5";
exports.ids = ["vendor-chunks/react-sliding-side-panel@2.0.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-sliding-side-panel@2.0.5/node_modules/react-sliding-side-panel/lib/index.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-sliding-side-panel@2.0.5/node_modules/react-sliding-side-panel/lib/index.js ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar react_transition_group_1 = __webpack_require__(/*! react-transition-group */ \"(ssr)/./node_modules/.pnpm/react-transition-group@4.4._3be4552f04086039bff5d8b5f4c9613b/node_modules/react-transition-group/esm/index.js\");\nvar getPanelGlassStyle = function (type, size, hidden) {\n    var horizontal = type === 'bottom' || type === 'top';\n    return __assign(__assign(__assign({ width: horizontal ? (hidden ? '0' : '100') + \"vw\" : 100 - size + \"vw\", height: horizontal ? 100 - size + \"vh\" : (hidden ? '0' : '100') + \"vh\" }, (type === 'right' && { left: 0 })), (type === 'top' && { bottom: 0 })), { position: 'inherit' });\n};\nvar getPanelStyle = function (type, size) {\n    var horizontal = type === 'bottom' || type === 'top';\n    return __assign(__assign(__assign({ width: horizontal ? '100vw' : size + \"vw\", height: horizontal ? size + \"vh\" : '100vh' }, (type === 'right' && { right: 0 })), (type === 'bottom' && { bottom: 0 })), { position: 'inherit', overflow: 'auto' });\n};\nvar SlidingPanel = function (_a) {\n    var type = _a.type, size = _a.size, panelContainerClassName = _a.panelContainerClassName, panelClassName = _a.panelClassName, isOpen = _a.isOpen, onOpen = _a.onOpen, onOpening = _a.onOpening, onOpened = _a.onOpened, onClose = _a.onClose, onClosing = _a.onClosing, onClosed = _a.onClosed, backdropClicked = _a.backdropClicked, noBackdrop = _a.noBackdrop, children = _a.children;\n    var glassBefore = type === 'right' || type === 'bottom';\n    var horizontal = type === 'bottom' || type === 'top';\n    return (React.createElement(\"div\", null,\n        React.createElement(\"div\", { className: \"sliding-panel-container \" + (isOpen ? 'active' : '') + \" \" + (noBackdrop ? 'click-through' : '') },\n            React.createElement(react_transition_group_1.CSSTransition, { in: isOpen, timeout: 500, classNames: \"panel-container-\" + type, unmountOnExit: true, onEnter: onOpen, onEntering: onOpening, onEntered: onOpened, onExit: onClose, onExiting: onClosing, onExited: onClosed, style: { display: horizontal ? 'block' : 'flex' } },\n                React.createElement(\"div\", null,\n                    glassBefore && (React.createElement(\"div\", { className: \"glass\", style: getPanelGlassStyle(type, size, !!noBackdrop), onClick: function (e) {\n                            if (!noBackdrop && backdropClicked)\n                                backdropClicked(e);\n                        } })),\n                    React.createElement(\"div\", { className: \"panel \" + (panelContainerClassName || ''), style: getPanelStyle(type, size) },\n                        React.createElement(\"div\", { className: \"panel-content \" + (panelClassName || '') }, children)),\n                    !glassBefore && (React.createElement(\"div\", { className: \"glass\", style: getPanelGlassStyle(type, size, !!noBackdrop), onClick: function (e) {\n                            if (!noBackdrop && backdropClicked)\n                                backdropClicked(e);\n                        } })))))));\n};\nSlidingPanel.defaultProps = {\n    type: 'left',\n    size: 50,\n    panelClassName: '',\n    panelContainerClassName: '',\n    noBackdrop: false,\n    children: null,\n};\nexports[\"default\"] = SlidingPanel;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-sliding-side-panel@2.0.5/node_modules/react-sliding-side-panel/lib/index.js\n");

/***/ })

};
;