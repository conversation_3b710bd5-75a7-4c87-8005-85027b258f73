import gql from 'graphql-tag'

export const DASHBOARD_VESSEL_LIST = gql`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                archived
                showOnDashboard
                title
                icon
                iconMode
                photoID
                registration
                callSign
                trainingsDue
                tasksDue
                logentryID
                logBookID
                lbeStartDate
                pob
                trainingStatus {
                    id
                    title
                    dueDate
                    isOverDue
                    member
                    memberId
                }
                taskStatus {
                    id
                    title
                    dueDate
                    isOverDue
                }
                vesselPosition {
                    id
                    lat
                    long
                    time
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                vesselCrewIds
            }
        }
    }
`
