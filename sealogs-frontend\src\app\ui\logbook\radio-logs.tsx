'use client'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_RADIO_LOGS } from '@/app/lib/graphQL/query'
import { CREATE_RADIO_LOG, UPDATE_RADIO_LOG } from '@/app/lib/graphQL/mutation'
import { useEffect, useState } from 'react'
import { X, Plus, Trash2 } from 'lucide-react'

import { SheetAlertDialog } from '@/components/ui/sheet-alert-dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { useSearchParams } from 'next/navigation'
import RadioTimeField from './components/radioTimeField'
import dayjs from 'dayjs'
import { Button } from '@/components/ui'
export default function RadioLogs({
    open,
    setOpen,
    logentryID,
}: {
    open: any
    setOpen: any
    logentryID: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [radioLogs, setRadioLogs] = useState<any>([])
    const [defaultRadioLogs, setDefaultRadioLogs] = useState<any>(false)
    const [displayLogAlert, setDisplayLogAlert] = useState(false)
    const [radioTitle, setRadioTitle] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentLog, setCurrentLog] = useState<any>(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)

    const [getRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setRadioLogs(data)
                if (defaultRadioLogs == false) {
                    getDefaultRadioLogs({
                        variables: {
                            filter: {
                                vesselID: { eq: +vesselID },
                            },
                        },
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    const [getDefaultRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setDefaultRadioLogs(true)
                if (data.length > 0) {
                    const logsToCreate = data
                        .filter(
                            (defaultLog: any) =>
                                !radioLogs.some(
                                    (log: any) =>
                                        log.defaultParent == defaultLog.id,
                                ),
                        )
                        .map((defaultLog: any) => ({
                            title: defaultLog.title,
                            logBookEntryID: logentryID,
                            defaultParent: +defaultLog.id,
                        }))

                    logsToCreate.forEach((log: any) => {
                        createRadioLog({
                            variables: {
                                input: {
                                    logBookEntryID: +logentryID,
                                    title: log.title,
                                    defaultParent: +log.defaultParent,
                                },
                            },
                        })
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    useEffect(() => {
        if (logentryID) {
            getRadioLogs({
                variables: {
                    filter: {
                        logBookEntryID: { eq: logentryID },
                    },
                },
            })
        }
    }, [])

    useEffect(() => {
        if (defaultRadioLogs && defaultRadioLogs.length > 0) {
        }
    }, [defaultRadioLogs])

    const [createRadioLog] = useMutation(CREATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            logBookEntryID: { eq: logentryID },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('createRadioLog error', error)
        },
    })

    const [updateRadioLog] = useMutation(UPDATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            logBookEntryID: { eq: logentryID },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('updateRadioLog error', error)
        },
    })

    const handleAddRadioLog = () => {
        setDisplayLogAlert(false)
        if (currentLog) {
            updateRadioLog({
                variables: {
                    input: {
                        id: currentLog.id,
                        title: radioTitle,
                    },
                },
            })
        } else {
            createRadioLog({
                variables: {
                    input: {
                        logBookEntryID: +logentryID,
                        title: radioTitle,
                    },
                },
            })
        }
    }

    const handleLogCheck = (log: any, time: any) => {
        updateRadioLog({
            variables: {
                input: {
                    id: log.id,
                    time: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
                },
            },
        })
    }

    return (
        <div className="w-full pb-16">
            <Sheet open={open} onOpenChange={setOpen}>
                <SheetContent
                    side="right"
                    className="w-[60%] sm:max-w-none bg-orange-100 p-0 border-none">
                    <div className="h-full flex flex-col">
                        <SheetHeader className="items-center flex justify-between font-medium py-4 px-6 rounded-tl-lg bg-orange-400">
                            <SheetTitle className="text-left text-white">
                                Radio Logs
                            </SheetTitle>
                            <button
                                className="rounded-full hover:bg-orange-500/20 p-2 transition-colors"
                                onClick={() => setOpen(false)}>
                                <X className="h-5 w-5 text-white" />
                            </button>
                        </SheetHeader>
                        <div className="flex flex-col px-6 py-4 h-full items-start">
                            {radioLogs && radioLogs.length > 0 ? (
                                <div className="w-full">
                                    {radioLogs.map((log: any) => (
                                        <div
                                            key={log.id}
                                            className="flex flex-row gap-2 mb-2 justify-between items-center">
                                            <span className="text-sm lg:text-base">
                                                <Button
                                                    onClick={() => {
                                                        setDisplayLogAlert(true)
                                                        setCurrentLog(log)
                                                    }}>
                                                    {log.title}
                                                </Button>
                                            </span>
                                            <div className="flex flex-row gap-2">
                                                <RadioTimeField
                                                    log={log}
                                                    handleTimeChange={
                                                        handleLogCheck
                                                    }
                                                />
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    iconLeft={Trash2}
                                                    onClick={() => {
                                                        updateRadioLog({
                                                            variables: {
                                                                input: {
                                                                    id: log.id,
                                                                    logBookEntryID: 0,
                                                                },
                                                            },
                                                        })
                                                    }}>
                                                    Delete
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex justify-center items-start h-full">
                                    <p className="text-gray-500">
                                        No Radio Logs
                                    </p>
                                </div>
                            )}
                            <Button
                                variant="text"
                                iconLeft={Plus}
                                onClick={() => {
                                    setDisplayLogAlert(true)
                                    setCurrentLog(false)
                                }}>
                                Add Radio Log
                            </Button>
                        </div>
                    </div>
                </SheetContent>
            </Sheet>

            <SheetAlertDialog
                openDialog={displayLogAlert}
                setOpenDialog={setDisplayLogAlert}
                handleCreate={handleAddRadioLog}
                actionText={currentLog ? 'Update' : 'Create'}
                title={`${currentLog ? 'Edit' : 'Create'} Radio Log`}
                sheetContext={true}>
                <div className="my-4">
                    <div className="flex flex-col w-full space-y-2">
                        <Label label="Location/Title">
                            <Input
                                type="text"
                                id="radioLogTitle"
                                placeholder="Enter Location/Title"
                                defaultValue={currentLog?.title}
                                required
                                onChange={(e) => {
                                    setRadioTitle(e.target.value)
                                }}
                            />
                        </Label>
                    </div>
                </div>
            </SheetAlertDialog>

            <SheetAlertDialog
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={() => {
                    updateRadioLog({
                        variables: {
                            input: {
                                id: currentLog.id,
                                comment: currentComment,
                            },
                        },
                    })
                    setOpenCommentAlert(false)
                }}
                title="Comment"
                actionText="Update"
                sheetContext={true}>
                <div className="my-4">
                    <Label label="Comment">
                        <Textarea
                            id="radioLogComment"
                            placeholder="Enter Comment"
                            defaultValue={currentComment}
                            rows={4}
                            required
                            onChange={(e) => {
                                setCurrentComment(e.target.value)
                            }}
                        />
                    </Label>
                </div>
            </SheetAlertDialog>
        </div>
    )
}
