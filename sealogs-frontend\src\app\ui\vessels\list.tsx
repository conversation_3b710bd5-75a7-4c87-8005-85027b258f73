'use client'
import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Link from 'next/link'
import { <PERSON><PERSON>, DialogTrigger, Heading, Popover } from 'react-aria-components'
import { DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { useSidebar } from '@/components/ui/sidebar'
import VesselPOB from './vessel-pob'
import VesselIcon from './vesel-icon'
import LocationMap from '@/components/location-map'
import { ArrowLeft, MoreHorizontal, X } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { getDashboardVesselList } from '@/app/lib/actions'
import { getVesselList } from '@/app/lib/actions'
import { isCrew } from '@/app/helpers/userHelper'
import { isEmpty, trim } from 'lodash'
import { SeaLogsButton } from '../daily-checks/Components'
import { VESSEL_STATUS } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { CREATE_VESSELSTATUS } from '@/app/lib/graphQL/mutation'
import toast, { Toaster } from 'react-hot-toast'
import DateField from '@/components/ui/date-field'
import Select, { StylesConfig } from 'react-select'
import Editor from '../editor'
import VesselsStatusEdit from './status-edit-popover'
import { mergeTrainingSessionDues } from '@/app/helpers/trainingHelper'
import { useRouter } from 'next/navigation'
import PopoverWrapper from '@/components/ui/popover-wrapper'
import { Card } from '@/components/ui'
import {
    SealogsCrewIcon,
    SealogsDocumentLockerIcon,
    SealogsLogbookIcon,
    SealogsMaintenanceIcon,
    SealogsTrainingIcon,
    SealogsVesselsIcon,
} from '@/app/lib/icons'
import { H1, H2, P } from '@/components/ui/typography'
import { VesselsFilterActions } from '@/components/filter/components/vessels-actions'

export default function VesselsList(props: any) {
    const router = useRouter()
    const [vessels, setVessels] = useState<any>([])
    const [archiveVessels, setArchiveVessels] = useState<any>([])
    const [activeVessels, setActiveVessels] = useState<any>([])
    const [showActiveVessels, setShowActiveVessels] = useState(true)
    const { isMobile } = useSidebar()
    const [loading, setLoading] = useState<boolean>(true)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    //const [vesselStatus, setVesselStatus] = useState<any>(false)
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const [vessel, setVessel] = useState<any>(false)

    // Fetch vessels using your action; expect each vessel to have an "archived" property.
    const handleSetVessels = (fetchedVessels: any) => {
        const active = fetchedVessels.filter((v: any) => !v.archived)
        const archived = fetchedVessels.filter((v: any) => v.archived)
        setActiveVessels(active)
        setArchiveVessels(archived)
        // Show active vessels by default
        setVessels(active)
        setLoading(false)
    }
    // Call your action to fetch vessels.
    getDashboardVesselList(handleSetVessels, 2)
    // Column definitions for the DataTable.
    const columns = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessels" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const [vesselStatus, setVesselStatus] = useState<any>(null)

                useEffect(() => {
                    getVesselStatus({ variables: { id: +vessel.id } })
                }, [vessel.id])

                const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
                    fetchPolicy: 'cache-and-network',
                    onCompleted: (response) => {
                        const data = response.readVesselStatuss.nodes
                        if (data) {
                            setVesselStatus(data[0])
                        }
                    },
                    onError: (error) => {
                        console.error('Error getting vessel status', error)
                    },
                })

                return (
                    <div>
                        {vesselStatus &&
                        vesselStatus.status !== 'OutOfService' ? (
                            <Link
                                className="flex flex-col md:flex-row gap-2 whitespace-nowrap items-center"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="border-2 border-bright-turquoise-600 rounded-full">
                                    <VesselIcon vessel={vessel} />
                                </div>
                                <div className="flex flex-col">
                                    <span className="font-medium">
                                        {vessel.title}
                                    </span>
                                    {vessel.logentryID !== 0 ? (
                                        <div className="text-accent text-[10px]">
                                            ON VOYAGE
                                        </div>
                                    ) : (
                                        <div className="text-accent text-[10px]">
                                            READY FOR VOYAGE
                                        </div>
                                    )}
                                </div>
                            </Link>
                        ) : (
                            <Link
                                className="flex flex-col md:flex-row gap-2 whitespace-nowrap items-center opacity-50"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="relative inline-block overflow-hidden border-2 border-cinnabar-600 rounded-full">
                                    <VesselIcon vessel={vessel} />
                                    <div className="absolute inset-0 bg-red-vivid-700 opacity-50 rounded-full">
                                        {'        '}
                                    </div>
                                </div>
                                <div className="flex flex-col">
                                    <span className="font-medium opacity-50">
                                        {vessel.title}
                                    </span>
                                    <div className="inline-block text-[10px] text-destructive">
                                        OUT OF SERVICE
                                    </div>
                                </div>
                            </Link>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'pob',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="P.O.B" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return <VesselPOB vessel={vessel} />
            },
        },
        {
            accessorKey: 'trainingsDue',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Training" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <>
                        {vessel.trainingsDue > 0 ? (
                            <Badge variant="destructive">
                                {vessel.trainingsDue}
                            </Badge>
                        ) : (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'tasksDue',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Tasks" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <>
                        {vessel.tasksDue > 0 ? (
                            <Badge variant="destructive">
                                {vessel.tasksDue}
                            </Badge>
                        ) : (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'postition',
            header: () => (
                <div className="h-8 flex !font-medium text-cool-grey-300 text-xs items-end">
                    Location
                </div>
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <>
                        {vessel.vesselPosition &&
                        (vessel.vesselPosition.geoLocation?.id > 0 ||
                            (vessel.vesselPosition.lat &&
                                vessel.vesselPosition.lat)) ? (
                            <DialogTrigger>
                                <SeaLogsButton icon="location" />
                                <Popover>
                                    <PopoverWrapper>
                                        <LocationMap
                                            position={[
                                                vessel.vesselPosition.lat ||
                                                    vessel.vesselPosition
                                                        .geoLocation?.lat,
                                                vessel.vesselPosition.long ||
                                                    vessel.vesselPosition
                                                        .geoLocation?.long,
                                            ]}
                                            zoom={7}
                                            vessel={vessel}
                                        />
                                        {
                                            vessel.vesselPosition.geoLocation
                                                ?.title
                                        }
                                    </PopoverWrapper>
                                </Popover>
                            </DialogTrigger>
                        ) : null}
                    </>
                )
            },
        },
        {
            id: 'actions',
            enableHiding: false,
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const [vesselStatus, setVesselStatus] = useState<any>(null)

                useEffect(() => {
                    getVesselStatus({ variables: { id: +vessel.id } })
                }, [vessel.id])

                const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
                    fetchPolicy: 'cache-and-network',
                    onCompleted: (response) => {
                        const data = response.readVesselStatuss.nodes
                        if (data) {
                            setVesselStatus(data[0])
                        }
                    },
                    onError: (error) => {
                        console.error('Error getting vessel status', error)
                    },
                })
                return (
                    <div className="flex items-center h-full">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <MoreHorizontal />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                                className="rounded-lg"
                                side={isMobile ? 'bottom' : 'right'}
                                align={isMobile ? 'end' : 'start'}>
                                <Link href={`/vessel/info?id=${vessel.id}`}>
                                    <DropdownMenuItem>
                                        <ArrowLeft className="icons h-6 w-6" />
                                        <span>View vessel</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=maintenance`}>
                                    <DropdownMenuItem>
                                        <SealogsMaintenanceIcon className="icons h-6 w-6" />
                                        <span>Maintenance</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=crew`}>
                                    <DropdownMenuItem>
                                        <SealogsCrewIcon className="icons h-6 w-6" />
                                        <span>Crew</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=crew_training`}>
                                    <DropdownMenuItem>
                                        <SealogsTrainingIcon className="icons h-6 w-6" />
                                        <span>Training & drills</span>
                                    </DropdownMenuItem>
                                </Link>
                                {vessel.logentryID !== 0 ? (
                                    <Link
                                        href={`/log-entries/view?vesselID=${vessel.id}&logentryID=${vessel.logentryID}`}>
                                        <DropdownMenuItem>
                                            <SealogsLogbookIcon className="icons h-6 w-6" />
                                            <span>Open Logbook entry</span>
                                        </DropdownMenuItem>
                                    </Link>
                                ) : (
                                    <Link href={`/vessel/info?id=${vessel.id}`}>
                                        <DropdownMenuItem>
                                            <SealogsLogbookIcon className="icons h-6 w-6" />
                                            <span>Logbook entry</span>
                                        </DropdownMenuItem>
                                    </Link>
                                )}
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=documents`}>
                                    <DropdownMenuItem>
                                        <SealogsDocumentLockerIcon className="icons h-6 w-6" />
                                        <span>Documents</span>
                                    </DropdownMenuItem>
                                </Link>
                                <DropdownMenuSeparator />
                                <div
                                    className="text-red-vivid-400"
                                    onClick={() => {
                                        setDisplayEditStatus(true),
                                            setVessel(vessel)
                                    }}>
                                    <DropdownMenuItem>
                                        <X className="border rounded-full w-5 h-5" />

                                        {vesselStatus &&
                                        vesselStatus.status !== 'OutOfService'
                                            ? 'Take vessel out of service'
                                            : 'Change vessel status'}
                                    </DropdownMenuItem>
                                </div>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                )
            },
        },
    ]

    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (data) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { name: { contains: data.value } },
                    { comments: { contains: data.value } },
                    { workOrderNumber: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }
        if (type === 'isArchived') {
            setVessels(!data ? archiveVessels : activeVessels)
        }
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
    }

    const handleDropdownChange = (type: string, data: any) => {
        handleFilterOnChange({ type, data })
    }

    return (
        <>
            <div className="bg-background phablet:bg-muted pb-[7px] z-50 sticky gap-4 pt-3 inset-0 flex items-start justify-between flex-nowrap">
                <div className="flex py-3 items-baseline">
                    <SealogsVesselsIcon
                        className={`h-12 w-12 ring-1 p-1 rounded-full`}
                    />
                    <H1 className="pl-4">All vessels</H1>
                </div>
                <VesselsFilterActions
                    onChange={(data: any) => {
                        handleDropdownChange('isArchived', data)
                    }}
                />
            </div>
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={vessels}
                    onChange={handleFilterOnChange}
                />
                {displayEditStatus && (
                    <VesselsStatusEdit
                        vessel={vessel}
                        display={displayEditStatus}
                        setDisplay={setDisplayEditStatus}
                    />
                )}
            </div>
        </>
    )
}
