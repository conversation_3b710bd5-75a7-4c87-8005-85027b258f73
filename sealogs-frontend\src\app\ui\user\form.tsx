'use client'
import { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import { debounce, isEmpty, trim } from 'lodash'
import { CREATE_USER, UPDATE_USER } from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import {
    getSeaLogsMembers,
    getVesselList,
    getSeaLogsGroups,
    getDepartmentList,
} from '@/app/lib/actions'
import CrewDutyDropdown from '@/components/filter/components/crew-duty-dropdown'
import DepartmentMultiSelectDropdown from '../department/multiselect-dropdown'

import toast, { Toaster } from 'react-hot-toast'
import { label } from 'yet-another-react-lightbox'
import { isAdmin } from '@/app/helpers/userHelper'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import {
    READ_ONE_CLIENT,
    READ_ONE_SEALOGS_MEMBER,
} from '@/app/lib/graphQL/query'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { SmartSelect } from '@/components/ui/SmartSelect'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { Check, ChevronLeft } from 'lucide-react'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { AlertDialogNew } from '@/components/ui'
import { Heading } from 'react-aria-components'

const UserForm = ({ userId }: { userId: number }) => {
    const [isUserAdmin, setIsUserAdmin] = useState<any>(-1)
    const router = useRouter()
    const [user, setUser] = useState<any>()
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [formErrors, setFormErrors] = useState({
        firstName: '',
        email: '',
        response: '',
    })
    const [userGroups, setUserGroups] = useState([])
    const [userVessels, setUserVessels] = useState([])
    const [groups, setGroups] = useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [departmentList, setDepartmentList] = useState<any>(false)
    const [selectedDepartments, setSelectedDepartments] = useState([] as any[])
    const [permissions, setPermissions] = useState<any>(false)
    const [isSelf, setIsSelf] = useState(false)
    const [currentDepartment, setCurrentDepartment] = useState<any>(false)
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const [companyDetail, setCompanyDetail] = useState<any>(null)
    const [openArchiveDialog, setOpenArchiveDialog] = useState(false)

    const [querySeaLogsMembers] = useLazyQuery(READ_ONE_SEALOGS_MEMBER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data) {
                setCurrentDepartment(data.departments.nodes)
            }
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembers error', error)
        },
    })

    useEffect(() => {
        querySeaLogsMembers({
            variables: {
                filter: { id: { eq: +(localStorage.getItem('userId') ?? 0) } },
            },
        })
    }, [])

    useEffect(() => {
        setPermissions(getPermissions)
        setIsUserAdmin(isAdmin())
    }, [])

    const handleSetUser = (members: any) => {
        const user = members[0]
        setUserVessels(
            user?.vehicles.nodes.map((vessel: any) => {
                return { label: vessel.title, value: vessel.id }
            }) ?? [],
        )

        const userGroups = user.groups.nodes.map((group: any) => {
            if (group) {
                const groupItem = groups.find((g: any) => g.value === group.id)
                return { label: groupItem.label, value: groupItem.value }
            }
        })[0] // user should only have one group/role

        if (userGroups) {
            setUserGroups(userGroups)
        }

        setUser({ ...user, primaryDutyID: user.primaryDuty?.id })
        const userDepartments = user.departments.nodes.map(
            (department: any) => {
                return department.id
            },
        )
        setSelectedDepartments(userDepartments)

        if (localStorage.getItem('userId') === user?.id) {
            setIsSelf(true)
        }
    }

    getSeaLogsMembers([userId], handleSetUser)

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const vesselsResponse = activeVessels.map((vessel: any) => {
            return {
                label: vessel.title,
                value: vessel.id,
            }
        })
        setVessels(vesselsResponse)
    }

    getVesselList(handleSetVessels)

    getDepartmentList(setDepartmentList)

    const handleSetGroups = (groups: any) => {
        let groupsResponse = groups
            .filter((group: any) => isAdmin() || group.code !== 'admin')
            .map((group: any) => {
                return {
                    label: group.title,
                    value: group.id,
                }
            })
        setGroups(groupsResponse)
    }

    getSeaLogsGroups(handleSetGroups)

    const debouncedhandleInputChange = debounce(
        (name: string, value: string) => {
            setUser({
                ...user,
                [name]: value,
                id: userId,
            })
        },
        300,
    )

    const handleInputChange = (e: any) => {
        const { name, value } = e.target

        debouncedhandleInputChange(name, value)
    }

    const handleGroupChange = (group: any) => {
        const groups = [group]
        setUserGroups(group) // user should only have one group/role
        setUser({
            ...user,
            groups: {
                nodes: groups.map((group: any) => {
                    return { id: group.value }
                }),
            },
        })
    }
    const handleVesselChange = (vessels: any) => {
        const vesselIDs = vessels.map((vessel: any) => {
            return vessel.value
        })
        setUserVessels(vessels)
        setUser({
            ...user,
            vehicles: vesselIDs,
        })
    }
    const handleDutyChange = (duty: any) => {
        setUser({
            ...user,
            primaryDutyID: +duty.value,
        })
    }
    const [mutationCreateUser, { loading: mutationCreateUserLoading }] =
        useMutation(CREATE_USER, {
            onCompleted: (response: any) => {
                const data = response.createSeaLogsMember
                if (data.id > 0) {
                    router.push('/crew')
                } else {
                    console.error('mutationCreateUser response error', response)
                }
            },
            onError: (error: any) => {
                setFormErrors({
                    ...formErrors,
                    response: error.message,
                })
                setHasFormErrors(true)
                console.error('mutationCreateUser catch error', error.message)
            },
        })
    const [mutationUpdateUser, { loading: mutationUpdateUserLoading }] =
        useMutation(UPDATE_USER, {
            onCompleted: (response: any) => {
                const data = response.updateSeaLogsMember
                if (data.id > 0) {
                    seaLogsMemberModel.delete(userId).then(() => {
                        router.push('/crew')
                    })
                } else {
                    console.error('mutationUpdateUser error', response)
                }
            },
            onError: (error: any) => {
                console.error('mutationUpdateUser error', error.message)
                toast.error(error.message)
            },
        })
    const handleSave = async () => {
        if (
            currentDepartment &&
            localStorage.getItem('useDepartment') === 'true'
        ) {
            if (selectedDepartments.length === 0) {
                toast.error('Please select a department')
                return
            }
        }
        if (
            user === undefined ||
            user?.groups === undefined ||
            userGroups.length === 0
        ) {
            toast.error('Please select a role')
            return
        }
        let hasErrors = false
        let errors = {
            firstName: '',
            email: '',
            response: '',
        }
        if (isEmpty(trim(user.firstName))) {
            hasErrors = true
            errors.firstName = 'First name is required'
        }
        if (isEmpty(trim(user.email))) {
            hasErrors = true
            errors.email = 'Email is required'
        }
        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            return
        }
        const variables = {
            input: {
                id: +user.id,
                firstName: user.firstName,
                surname: user.surname,
                username: user.username,
                password: user.password,
                email: user.email,
                isPilot: user.isPilot,
                isTransferee: user.isTransferee,
                phoneNumber: user.phoneNumber,
                ...(!isEmpty(user.groups) && {
                    groups: user.groups.nodes
                        .map((group: any) => group.id)
                        .join(','),
                }),
                ...(!isEmpty(userVessels) && {
                    vehicles: userVessels
                        .map((vessel: any) => vessel.value)
                        .join(','),
                }),
                primaryDutyID: +user.primaryDutyID,
                departments: selectedDepartments.join(','),
            },
        }
        if (userId === 0) {
            await mutationCreateUser({
                variables,
            })
        } else {
            await mutationUpdateUser({
                variables,
            })
        }
    }

    const handleDepartmentChange = (departments: any) => {
        setSelectedDepartments(departments.map((d: any) => d.value))
    }

    const filterByDepartment = (vessels: any) => {
        return (
            departmentList &&
            departmentList
                ?.filter(
                    (d: any) =>
                        d.seaLogsMembers.nodes?.find(
                            (m: any) => m.id == userId,
                        ) || selectedDepartments.includes(d.id),
                )
                ?.map((d: any) => d.basicComponents.nodes)
                .flat()
                ?.map((v: any) => {
                    return vessels.find((vessel: any) => vessel.value === v.id)
                })
        )
    }

    const handleArchiveUser = async (crewInfo: any) => {
        if (crewInfo && crewInfo.id > 0) {
            const variables = {
                input: {
                    id: crewInfo.id,
                    isArchived: !crewInfo.isArchived,
                },
            }
            await mutationUpdateUser({
                variables,
            })
        }
    }

    if (!permissions || !hasPermission('VIEW_MEMBER', permissions)) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <Card>
            <CardHeader className="border-b">
                <CardTitle>{userId === 0 ? 'Create' : 'Edit'} User</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
                <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-3 gap-6 p-6">
                    <div className="space-y-2">
                        <h3 className="text-lg font-medium">User Details</h3>
                        <p className="text-sm text-muted-foreground">
                            Personal information and account settings for this
                            user.
                        </p>
                    </div>
                    <div className="col-span-2 pt-8 pb-5 space-y-6 px-7 border rounded-lg">
                        {hasFormErrors && formErrors.response && (
                            <Alert variant="destructive" className="mb-4">
                                <AlertDescription>
                                    {formErrors.response}
                                </AlertDescription>
                            </Alert>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="firstName">First name</Label>
                                <Input
                                    id="firstName"
                                    name="firstName"
                                    placeholder="First name"
                                    type="text"
                                    required
                                    defaultValue={user?.firstName || ''}
                                    onChange={handleInputChange}
                                    className="w-full"
                                />
                                {hasFormErrors && formErrors.firstName && (
                                    <p className="text-sm text-destructive">
                                        {formErrors.firstName}
                                    </p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="surname">Surname</Label>
                                <Input
                                    id="surname"
                                    name="surname"
                                    placeholder="Surname"
                                    type="text"
                                    defaultValue={user?.surname || ''}
                                    onChange={handleInputChange}
                                    className="w-full"
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="username">Username</Label>
                                <Input
                                    id="username"
                                    name="username"
                                    placeholder="Username"
                                    type="text"
                                    defaultValue={user?.username || ''}
                                    onChange={handleInputChange}
                                    className="w-full"
                                />
                            </div>
                            {((permissions &&
                                hasPermission('ADMIN', permissions)) ||
                                isSelf) && (
                                <div className="space-y-2">
                                    <Label htmlFor="password">Password</Label>
                                    <Input
                                        id="password"
                                        name="password"
                                        placeholder="Password"
                                        type="password"
                                        required={userId === 0}
                                        onChange={handleInputChange}
                                        autoComplete="new-password"
                                        className="w-full"
                                    />
                                </div>
                            )}
                        </div>

                        {((permissions &&
                            hasPermission(
                                process.env.VIEW_MEMBER_CONTACT ||
                                    'VIEW_MEMBER_CONTACT',
                                permissions,
                            )) ||
                            isSelf) && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        placeholder="Email"
                                        defaultValue={user?.email || ''}
                                        onChange={handleInputChange}
                                        className="w-full"
                                    />
                                    {hasFormErrors && formErrors.email && (
                                        <p className="text-sm text-destructive">
                                            {formErrors.email}
                                        </p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="phoneNumber">
                                        Phone Number
                                    </Label>
                                    <Input
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        placeholder="Phone Number"
                                        type="tel"
                                        defaultValue={user?.phoneNumber || ''}
                                        onChange={handleInputChange}
                                        className="w-full"
                                    />
                                </div>
                            </div>
                        )}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2 flex flex-col">
                                <Label htmlFor="primaryDuty">
                                    Primary Duty
                                </Label>
                                <CrewDutyDropdown
                                    onChange={handleDutyChange}
                                    crewDutyID={user?.primaryDuty?.id || 0}
                                />
                            </div>

                            <div className="space-y-2 flex flex-col">
                                <Label htmlFor="userRole">User Role</Label>
                                {groups && (
                                    <Combobox
                                        placeholder="Select roles"
                                        onChange={handleGroupChange}
                                        value={userGroups}
                                        options={groups}
                                        multi
                                    />
                                )}
                            </div>

                            {localStorage.getItem('useDepartment') ===
                                'true' && (
                                <div className="space-y-2 flex flex-col">
                                    <Label htmlFor="departments">
                                        Departments
                                    </Label>
                                    {departmentList && (
                                        <DepartmentMultiSelectDropdown
                                            onChange={handleDepartmentChange}
                                            value={selectedDepartments}
                                            allDepartments={departmentList}
                                        />
                                    )}
                                    <p className="text-xs text-muted-foreground mt-1">
                                        The assigned departments will be cleared
                                        if this user is assigned the
                                        Administrator role.
                                    </p>
                                </div>
                            )}

                            <div className="space-y-2 flex flex-col">
                                <Label htmlFor="vessels">Vessels</Label>
                                {vessels && (
                                    <Combobox
                                        multi
                                        onChange={handleVesselChange}
                                        placeholder="Select vessels"
                                        options={
                                            localStorage.getItem(
                                                'useDepartment',
                                            ) === 'true'
                                                ? filterByDepartment(vessels)
                                                : vessels
                                        }
                                        value={userVessels}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <Separator />
            <FooterWrapper className="flex justify-end gap-2 p-4">
                <Button
                    variant="outline"
                    onClick={() => router.push('/crew')}
                    className="gap-2">
                    <ChevronLeft className="h-4 w-4" />
                    Back to Crew
                </Button>
                {permissions &&
                    hasPermission(
                        process.env.EDIT_MEMBER || 'EDIT_MEMBER',
                        permissions,
                    ) && (
                        <SeaLogsButton
                            type="text"
                            icon="record"
                            text={user?.isArchived ? 'Retrieve' : 'Archive'}
                            className={'mb-1 md:mb-0'}
                            action={() => {
                                setOpenArchiveDialog(true)
                            }}
                        />
                    )}
                <Button
                    onClick={handleSave}
                    disabled={
                        mutationCreateUserLoading || mutationUpdateUserLoading
                    }
                    className="gap-2">
                    <Check className="h-4 w-4" />
                    {userId === 0 ? 'Create user' : 'Update user'}
                </Button>
            </FooterWrapper>
            <AlertDialogNew
                openDialog={openArchiveDialog}
                setOpenDialog={setOpenArchiveDialog}
                actionText={user?.isArchived ? 'Retrieve' : 'Archive'}
                cancelText="Cancel"
                handleCreate={() => handleArchiveUser(user)}
                title={`${user?.isArchived ? 'Retrieve' : 'Archive'} User`}
                // description={`Are you sure you want to ${user?.isArchived ? 'retrieve' : 'archive'} ${user?.firstName || 'this user'} ${user?.surname || ''} ?`}
            >
                <div className="flex justify-center flex-col px-6 py-6">
                    {permissions &&
                    hasPermission(
                        process.env.DELETE_MEMBER || 'DELETE_MEMBER',
                        permissions,
                    ) ? (
                        <div>
                            Are you sure you want to{' '}
                            {user?.isArchived ? 'retrieve' : 'archive'}{' '}
                            {`${user?.firstName || 'this user'} ${user?.surname || ''}`}
                            ?
                        </div>
                    ) : (
                        <>
                            <Heading
                                slot="title"
                                className="text-2xl  leading-6 my-2 ">
                                Warning
                            </Heading>
                            <p className="mt-3 text-slate-500">
                                You do not have permission to archive user.
                            </p>
                            <hr className="my-6" />
                            <div className="flex justify-end">
                                <Button
                                    className="mr-8"
                                    onClick={() => setOpenArchiveDialog(false)}>
                                    Cancel
                                </Button>
                            </div>
                        </>
                    )}
                </div>
            </AlertDialogNew>
            <Toaster position="top-right" />
        </Card>
    )
}

export default UserForm
