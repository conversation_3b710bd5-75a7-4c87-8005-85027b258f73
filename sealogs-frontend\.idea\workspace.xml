<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="995c52bc-f2a4-4a62-bb9a-23f71c44f478" name="Changes" comment="UI Redesign and Migrations for log entry components">
      <change afterPath="$PROJECT_DIR$/src/app/hooks/use-crew-member-name.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/hooks/useCrewContext.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/crew/info/page.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/crew/info/page.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/ui/crew-training/list.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/crew-training/list.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/ui/crew/allocated-tasks.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/crew/allocated-tasks.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/ui/crew/view.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/crew/view.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/ui/crew/voyages.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/crew/voyages.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/filter/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/filter/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/ui/badge.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/ui/badge.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/ui/breadcrumb-navigation.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/ui/breadcrumb-navigation.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/ui/button.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/ui/button.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;brentPhil&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/SeaLogs/SeaLogsV2.git&quot;,
    &quot;accountId&quot;: &quot;d6e90467-2c27-42b9-826f-ddf33c9458fb&quot;
  },
  &quot;recentNewPullRequestHead&quot;: {
    &quot;server&quot;: {
      &quot;useHttp&quot;: false,
      &quot;host&quot;: &quot;github.com&quot;,
      &quot;port&quot;: null,
      &quot;suffix&quot;: null
    },
    &quot;owner&quot;: &quot;SeaLogs&quot;,
    &quot;repository&quot;: &quot;SeaLogsV2&quot;
  }
}</component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="PHP Herd" />
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2tZVCosWyXOfwZytKESS3qPJbBP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;brent-develop&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.client&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.server&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Documents/GitHub/SeaLogsV2/sealogs-frontend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;C:\\Users\\<USER>\\Documents\\GitHub\\SeaLogsV2\\sealogs-frontend\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Documents\\GitHub\\SeaLogsV2\\sealogs-frontend\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-WS-251.23774.424" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="995c52bc-f2a4-4a62-bb9a-23f71c44f478" name="Changes" comment="" />
      <created>1740559459611</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740559459611</updated>
      <workItem from="1740559461505" duration="1715000" />
      <workItem from="1740571466351" duration="1113000" />
      <workItem from="1740643513772" duration="57000" />
      <workItem from="1740654866720" duration="1841000" />
      <workItem from="1740715888099" duration="7000" />
      <workItem from="1740978583455" duration="123000" />
      <workItem from="1740988073978" duration="1781000" />
      <workItem from="1740995615122" duration="1498000" />
      <workItem from="1742907298791" duration="64000" />
      <workItem from="1743156646977" duration="1456000" />
      <workItem from="1743390075554" duration="339000" />
      <workItem from="1743420238073" duration="1584000" />
      <workItem from="1743492920995" duration="4287000" />
      <workItem from="1743597212243" duration="381000" />
      <workItem from="1743679041910" duration="1347000" />
      <workItem from="1743685070173" duration="12000" />
      <workItem from="1746002937077" duration="392000" />
      <workItem from="1746003358105" duration="7914000" />
      <workItem from="1746086908346" duration="49000" />
      <workItem from="1746086964481" duration="3181000" />
      <workItem from="1746090198093" duration="22000" />
      <workItem from="1746090239164" duration="476000" />
      <workItem from="1746090732214" duration="2400000" />
      <workItem from="1746159180949" duration="261000" />
      <workItem from="1746159463317" duration="25060000" />
      <workItem from="1746414716584" duration="1759000" />
      <workItem from="1746424879997" duration="11439000" />
      <workItem from="1746497025655" duration="650000" />
      <workItem from="1746502269996" duration="13000" />
      <workItem from="1746507668253" duration="4733000" />
      <workItem from="1746585407383" duration="2504000" />
      <workItem from="1746619320964" duration="5000" />
      <workItem from="1746679546977" duration="23000" />
      <workItem from="1746775089776" duration="6000" />
      <workItem from="1747044876144" duration="2000" />
      <workItem from="1747065748569" duration="8000" />
      <workItem from="1747124941167" duration="49000" />
      <workItem from="1747139442965" duration="2974000" />
      <workItem from="1747156603251" duration="3000" />
      <workItem from="1747161859624" duration="4000" />
      <workItem from="1747281478838" duration="272000" />
      <workItem from="1747288121587" duration="6000" />
      <workItem from="1747311911127" duration="8000" />
      <workItem from="1747993023166" duration="2000" />
      <workItem from="1748159821584" duration="596000" />
      <workItem from="1748419545439" duration="598000" />
      <workItem from="1748431098111" duration="793000" />
    </task>
    <task id="LOCAL-00001" summary="Refactor forms and enhance login/lost password UI&#10;&#10;Switch lost password and login forms to use `react-hook-form` with Zod for validation, replacing Formik and Yup. Update layout and UI components to improve structure, responsiveness, and user experience. Add background image and white logo variants for enhanced visual appeal.">
      <option name="closed" value="true" />
      <created>1740571820790</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1740571820790</updated>
    </task>
    <task id="LOCAL-00002" summary="Refactor date range picker and dropdown components.&#10;&#10;Updated DateRange component to use Popover, Calendar, and styled buttons. Replaced Select with Combobox in several dropdowns for enhanced functionality and consistency. Optimized code readability and applied minor UI fixes across various form components.">
      <option name="closed" value="true" />
      <created>1740655196741</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1740655196741</updated>
    </task>
    <task id="LOCAL-00003" summary="Replace third-party select components with custom Combobox&#10;&#10;Replaced instances of react-select and SLSelect with a unified and reusable `Combobox` component across various dropdowns. This enhances maintainability and styling consistency while leveraging the custom UI components. Updated related">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00004" summary="Use URL query params in vessels module for better routing&#10;&#10;Updated vessel-related URLs to include query parameters like `name` for improved routing and navigation consistency. Removed redundant imports and adjusted breadcrumb logic to dynamically reflect query parameters, enhancing user experience.">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00005" summary="Replace Combobox with SmartSelect and input with Input&#10;&#10;Refactored various components to use SmartSelect and Input for consistency and improved functionality. The changes include enhanced management of selected values, better support for multiple selection, and improved validation logic.">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00006" summary="Replace Select with Combobox and simplify components&#10;&#10;Refactored various components to replace react-select with Combobox for better consistency and simplified usage. Removed unnecessary custom styles and classNames, streamlined props usage, and improved value handling. This enhances performance and maintainability across the codebase.">
      <option name="closed" value="true" />
      <created>1743420498885</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1743420498885</updated>
    </task>
    <task id="LOCAL-00007" summary="Replace Select and textarea components with Combobox and Textarea&#10;&#10;Replaced all instances of the Select component with the Combobox component and textarea with the Textarea component for consistency and improved functionality. Removed unnecessary classNames and configurations, simplifying the codebase while preserving behavior.">
      <option name="closed" value="true" />
      <created>1743422716543</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1743422716543</updated>
    </task>
    <task id="LOCAL-00008" summary="Refactor components to use improved Combobox and Skeleton&#10;&#10;Replaced Select with Combobox and standardized Skeleton imports across the project for consistency and better functionality. Updated related code with simplified configurations, ensuring cleaner and more maintainable implementations.">
      <option name="closed" value="true" />
      <created>1743493065238</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1743493065238</updated>
    </task>
    <task id="LOCAL-00009" summary="Add new dialogs for crew member creation and alerts&#10;&#10;Introduced `AddCrewMemberDialog` for adding crew members and a reusable `AlertDialog` component to handle alert modals across the application. These enhancements improve UI modularity and functionality.">
      <option name="closed" value="true" />
      <created>1743493086390</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1743493086390</updated>
    </task>
    <task id="LOCAL-00010" summary="resolve merge conflicts">
      <option name="closed" value="true" />
      <created>1743494667254</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1743494667254</updated>
    </task>
    <task id="LOCAL-00011" summary="resolve merge conflicts">
      <option name="closed" value="true" />
      <created>1743495641537</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1743495641537</updated>
    </task>
    <task id="LOCAL-00012" summary="Refactor AlertDialog to AlertDialogNew and replace spans with Labels&#10;&#10;Updated AlertDialog components to AlertDialogNew across various forms to align with new component standards. Replaced HTML spans with Label elements for improved semantics and consistency in UI.">
      <option name="closed" value="true" />
      <created>1743506479382</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1743506479382</updated>
    </task>
    <task id="LOCAL-00013" summary="Refactor signature pad to shared component and cleanup UI&#10;&#10;Consolidated multiple `SignaturePad` implementations into a shared reusable component for consistency and maintainability. Enhanced multiple forms, including User and Training forms, with modernized UI components like `Card` and `Alert` for better user experience.">
      <option name="closed" value="true" />
      <created>1743597547792</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1743597547792</updated>
    </task>
    <task id="LOCAL-00014" summary="``` &#10;Refactor logbook UI and integrate improved toast notifications.&#10;&#10;Updated the LogBookEntry and related components for better UI with new Radix primitives, ensuring consistency across the layout. Replaced `react-hot-toast` with a custom `useToast` hook for more flexible and styled notifications. Streamlined LogDate component logic and added animations for better user experience.&#10;```">
      <option name="closed" value="true" />
      <created>1743680321991</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1743680321991</updated>
    </task>
    <task id="LOCAL-00015" summary="Remove the default variant from buttons and other elements&#10;&#10;The `default` button styling variant was removed to simplify the code and reduce redundancy across the UI components. Unused or legacy props were cleaned up, and component styling improvements were made for better alignment with design guidelines. Additionally, internal state management and accessibility features were enhanced for certain components.">
      <option name="closed" value="true" />
      <created>1746005266809</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746005266809</updated>
    </task>
    <task id="LOCAL-00016" summary="major bug fixes and UI improvements">
      <option name="closed" value="true" />
      <created>1746023899125</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1746023899125</updated>
    </task>
    <task id="LOCAL-00017" summary="Enhanced crew duty nesting and UI">
      <option name="closed" value="true" />
      <created>1746174658790</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1746174658790</updated>
    </task>
    <task id="LOCAL-00018" summary="UI enhancements and date picker functionality fix">
      <option name="closed" value="true" />
      <created>1746197844768</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1746197844768</updated>
    </task>
    <task id="LOCAL-00019" summary="feat(daily-checks): standardize UI patterns across pre-departure checks&#10;&#10;- Replace custom fields with DailyCheckField component across all tabs&#10;- Update toast implementations to use Shadcn toast system&#10;- Migrate create task sheet pop-up form to Shadcn components&#10;- Apply consistent UI patterns and styling across all pre-departure check tabs">
      <option name="closed" value="true" />
      <created>1746450792580</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1746450792580</updated>
    </task>
    <task id="LOCAL-00020" summary="UI Redesign and Migrations for log entry components">
      <option name="closed" value="true" />
      <created>1746532946520</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1746532946520</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Refactor forms and enhance login/lost password UI&#10;&#10;Switch lost password and login forms to use `react-hook-form` with Zod for validation, replacing Formik and Yup. Update layout and UI components to improve structure, responsiveness, and user experience. Add background image and white logo variants for enhanced visual appeal." />
    <MESSAGE value="Refactor date range picker and dropdown components.&#10;&#10;Updated DateRange component to use Popover, Calendar, and styled buttons. Replaced Select with Combobox in several dropdowns for enhanced functionality and consistency. Optimized code readability and applied minor UI fixes across various form components." />
    <MESSAGE value="Replace third-party select components with custom Combobox&#10;&#10;Replaced instances of react-select and SLSelect with a unified and reusable `Combobox` component across various dropdowns. This enhances maintainability" />
    <MESSAGE value="Use URL query params in vessels module for better routing&#10;&#10;Updated vessel-related URLs to include query parameters like `name` for improved routing and navigation consistency. Removed redundant imports and adjusted breadcrumb logic to dynamically reflect query parameters, enhancing user experience." />
    <MESSAGE value="Update TypeScript reference link in next-env.d.ts&#10;&#10;Replaced outdated URL with the current link to TypeScript configuration documentation in Next.js. This ensures developers access the correct and up-to-date resources directly from the code comments." />
    <MESSAGE value="Replace Combobox with SmartSelect and input with Input&#10;&#10;Refactored various components to use SmartSelect and Input for consistency and improved functionality. The changes include enhanced management of selected values, better support for multiple selection, and improved validation logic." />
    <MESSAGE value="Replace Select with Combobox and simplify components&#10;&#10;Refactored various components to replace react-select with Combobox for better consistency and simplified usage. Removed unnecessary custom styles and classNames, streamlined props usage, and improved value handling. This enhances performance and maintainability across the codebase." />
    <MESSAGE value="Replace Select and textarea components with Combobox and Textarea&#10;&#10;Replaced all instances of the Select component with the Combobox component and textarea with the Textarea component for consistency and improved functionality. Removed unnecessary classNames and configurations, simplifying the codebase while preserving behavior." />
    <MESSAGE value="Refactor components to use improved Combobox and Skeleton&#10;&#10;Replaced Select with Combobox and standardized Skeleton imports across the project for consistency and better functionality. Updated related code with simplified configurations, ensuring cleaner and more maintainable implementations." />
    <MESSAGE value="Add new dialogs for crew member creation and alerts&#10;&#10;Introduced `AddCrewMemberDialog` for adding crew members and a reusable `AlertDialog` component to handle alert modals across the application. These enhancements improve UI modularity and functionality." />
    <MESSAGE value="resolve merge conflicts" />
    <MESSAGE value="Refactor AlertDialog to AlertDialogNew and replace spans with Labels&#10;&#10;Updated AlertDialog components to AlertDialogNew across various forms to align with new component standards. Replaced HTML spans with Label elements for improved semantics and consistency in UI." />
    <MESSAGE value="Refactor signature pad to shared component and cleanup UI&#10;&#10;Consolidated multiple `SignaturePad` implementations into a shared reusable component for consistency and maintainability. Enhanced multiple forms, including User and Training forms, with modernized UI components like `Card` and `Alert` for better user experience." />
    <MESSAGE value="Enhance DateRange component with time selection and validation&#10;&#10;Added support for time selection, date validation, and customization options in the DateRange component. Introduced new props like `includeTime`, `validation`, and `numberOfMonths`. Improved user experience with flexible date-time handling and enhanced functionality." />
    <MESSAGE value="Add reusable Switch, AlertDialogNew, and TimePicker components&#10;&#10;Introduced three new components: Switch for toggling states, AlertDialogNew for customizable alert dialogs, and TimePicker for selecting and managing time with various configurations. These components enhance the UI library's functionality and reusability." />
    <MESSAGE value="``` &#10;Refactor logbook UI and integrate improved toast notifications.&#10;&#10;Updated the LogBookEntry and related components for better UI with new Radix primitives, ensuring consistency across the layout. Replaced `react-hot-toast` with a custom `useToast` hook for more flexible and styled notifications. Streamlined LogDate component logic and added animations for better user experience.&#10;```" />
    <MESSAGE value="Remove the default variant from buttons and other elements&#10;&#10;The `default` button styling variant was removed to simplify the code and reduce redundancy across the UI components. Unused or legacy props were cleaned up, and component styling improvements were made for better alignment with design guidelines. Additionally, internal state management and accessibility features were enhanced for certain components." />
    <MESSAGE value="major bug fixes and UI improvements" />
    <MESSAGE value="Refactor multi-select and button variants; update imports.&#10;&#10;Refined multi-select logic in comboBox for clarity and reliability, ensuring appropriate handling of &quot;select all&quot; use cases. Updated button variants to align with primary/secondary conventions, and enhanced import paths for consistency." />
    <MESSAGE value="Enhanced crew duty nesting and UI" />
    <MESSAGE value="UI enhancements and date picker functionality fix" />
    <MESSAGE value="feat(daily-checks): standardize UI patterns across pre-departure checks&#10;&#10;- Replace custom fields with DailyCheckField component across all tabs&#10;- Update toast implementations to use Shadcn toast system&#10;- Migrate create task sheet pop-up form to Shadcn components&#10;- Apply consistent UI patterns and styling across all pre-departure check tabs" />
    <MESSAGE value="UI Redesign and Migrations for log entry components" />
    <option name="LAST_COMMIT_MESSAGE" value="UI Redesign and Migrations for log entry components" />
  </component>
</project>